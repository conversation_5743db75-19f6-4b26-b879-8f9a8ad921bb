import { useGSAP } from "@gsap/react";
import gsap from "gsap";
import { useEffect, useRef } from "react";
import { ScrollTrigger } from "gsap/ScrollTrigger";

const Showcase = () => {
  const containerRef = useRef(null);
  const lastRef = useRef(null);
  gsap.registerPlugin(ScrollTrigger);
  useEffect(() => {
    // Initialize Shery effects after component mounts
    if (window.Shery) {
      Shery.imageEffect(".images", {
        style: 4,
        config: { onMouse: { value: 1 } },
        slideStyle: (setScroll) => {
          sections.forEach(function (section, index) {
            ScrollTrigger.create({
              trigger: section,
              start: "top top",
              scrub: 1,
              onUpdate: function (prog) {
                setScroll(prog.progress + index);
              },
            });
          });
        },
      });
    }
  }, []);

  useGSAP(() => {
    gsap.registerPlugin(ScrollTrigger); // Already registered above, but safe to keep
    gsap.utils.toArray(".leftelem").forEach((elem) => {
      gsap.to(elem, {
        yPercent: -100,
        duration: 3,
        ease: "none",
        scrollTrigger: {
          trigger: containerRef.current,
          pin: true,
          start: "top top",
          end: "bottom bottom",
          markers: true,
          endTrigger: lastRef.current,
          scrub: 1,
        },
      });
    });
  }, []);

  return (
    <div
      ref={containerRef}
      className="w-full h-screen bg-black relative z-20 flex justify-between rounded-t-[3rem] overflow-hidden"
    >
      <div className="left w-[60%] h-full relative overflow-hidden font-['abrikos']  text-white">
        <div className="leftelem w-full h-full p-20 flex  justify-center flex-col">
          <p className="tracking-widest text-md">ROSIER FOOD</p>
          <h2 className=" text-8xl my-6">Wild Forest Honey</h2>
          <div className="desc w-full tracking-wide font-[satoshi] mt-6">
            <p className="text-2xl leading-none">
              Wild Flower Honey is a multi-floral honey, responsibly collected
              from bees feeding on wild forest flowers nectar from the forest of
              the Himalayas. The honey is rich in bio-diverse vitamins,
              minerals, and amino acids boosting good health. 100% Natural |
              Ayurvedic | No added sugar.
            </p>
          </div>
        </div>
        <div className="leftelem w-full h-full p-20 flex  justify-center flex-col">
          <p className="tracking-widest text-md">ROSIER FOOD</p>
          <h2 className=" text-8xl my-6">Wild Forest Honey</h2>
          <div className="desc w-full tracking-wide font-[satoshi] mt-6">
            <p className="text-2xl leading-none">
              Wild Flower Honey is a multi-floral honey, responsibly collected
              from bees feeding on wild forest flowers nectar from the forest of
              the Himalayas. The honey is rich in bio-diverse vitamins,
              minerals, and amino acids boosting good health. 100% Natural |
              Ayurvedic | No added sugar.
            </p>
          </div>
        </div>
        <div
          ref={lastRef}
          className="leftelem w-full h-full p-20 flex  justify-center flex-col"
        >
          <p className="tracking-widest text-md">ROSIER FOOD</p>
          <h2 className=" text-8xl my-6">Wild Forest Honey</h2>
          <div className="desc w-full tracking-wide font-[satoshi] mt-6">
            <p className="text-2xl leading-none">
              Wild Flower Honey is a multi-floral honey, responsibly collected
              from bees feeding on wild forest flowers nectar from the forest of
              the Himalayas. The honey is rich in bio-diverse vitamins,
              minerals, and amino acids boosting good health. 100% Natural |
              Ayurvedic | No added sugar.
            </p>
          </div>
        </div>
      </div>
      <div className="right h-full w-[40%] relative">
        <div className="images w-full h-full">
          <img src="Honey.png" alt="" className="w-full h-full object-cover" />
        </div>
      </div>
    </div>
  );
};

export default Showcase;
